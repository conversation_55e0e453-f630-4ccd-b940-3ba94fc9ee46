-- ============================================================================
-- Copyright (C) 2023
--
-- This program is free software; you can redistribute it and/or modify
-- it under the terms of the GNU General Public License as published by
-- the Free Software Foundation; either version 3 of the License, or
-- (at your option) any later version.
--
-- This program is distributed in the hope that it will be useful,
-- but WITHOUT ANY WARRANTY; without even the implied warranty of
-- MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
-- GNU General Public License for more details.
--
-- You should have received a copy of the GNU General Public License
-- along with this program. If not, see <https://www.gnu.org/licenses/>.
--
-- ============================================================================

CREATE TABLE llx_rendez_vous_demo (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_projet integer NOT NULL,
  demo_name varchar(255) NOT NULL,
  demo_url varchar(500),
  demo_path varchar(500),
  demo_db_name varchar(255),
  modules_selectionnes text,
  date_creation datetime,
  date_expiration datetime,
  statut tinyint DEFAULT 1,
  notes text
) ENGINE=innodb;
