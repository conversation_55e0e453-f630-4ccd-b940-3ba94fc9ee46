<?php
/**
 *	\file       rendezvousclient/site/parametrage.php
 *	\ingroup    rendezvousclient
 *	\brief      fiche parametrage des sites
 */

require_once '../../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/societe/class/societe.class.php';
require_once DOL_DOCUMENT_ROOT.'/projet/class/project.class.php';
dol_include_once('/rendezvousclient/lib/rendezvousclient.lib.php');
dol_include_once('/rendezvousclient/site/class/site.class.php');
dol_include_once('/rendezvousclient/site/class/democreator.class.php');
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.formother.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/doleditor.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';


// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'users', 'companies'));

$id = GETPOST('id', 'int');
$action = GETPOST('action', 'az09');

$site = new Site($db);
if($id > 0){
    $site->fetch($id);
}

$projectstatic = new Project($db);
if($site->fk_projet > 0){
    $projectstatic->fetch($site->fk_projet);
}

$societe = new Societe($db);
if($projectstatic->socid > 0){
    $societe->fetch($projectstatic->socid);
}

/*
 * Actions
*/

if($action == 'update'){

    print '<pre>'.print_r($_POST, true).'</pre>';
    exit;

}

if($action == 'createdemo'){
    // Récupérer les modules sélectionnés
    $parametragesite = new ParametrageSite($db);
    $parametragesite->fetch($site->rowid);

    $modules_selectionnes = array();
    foreach($parametragesite->modules as $module_id => $module){
        if($module['checked'] == 1){
            $modules_selectionnes[] = $module_id;
        }
    }

    // Créer la démo Dolibarr
    $demo_creator = new DemoCreator($db);
    $demo_url = $demo_creator->createDemo($site, $modules_selectionnes);

    if($demo_url){
        setEventMessages($langs->trans("DemoCreatedSuccessfully").' : <a href="'.$demo_url.'" target="_blank">'.$demo_url.'</a>', null, 'mesgs');
    } else {
        setEventMessages($langs->trans("ErrorDemoCreation"), null, 'errors');
    }

    header("Location: ".$_SERVER['PHP_SELF']."?id=".$id);
    exit;
}



/*
 * View
*/

$form = new Form($db);

$formother = new FormOther($db);

$title = $langs->trans('Site').' - '.$langs->trans('Settings');

llxHeader('', $title, $help_url);

/*
 *  View
*/

$site->socid = $societe->id;

$head = site_prepare_head($site);

print dol_get_fiche_head($head, 'parametrage', $langs->trans("Site"), -1, 'generic');

print '
<div class="arearef heightref valignmiddle centpercent">
    <!-- Start banner content -->
    <div style="vertical-align: middle">
        <div class="pagination paginationref">
            <ul class="right">
                <li class="noborder litext clearbothonsmartphone">
                    <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Retour liste</a>
                </li>
            </ul>
        </div>
        <!-- morehtmlleft -->
        <div class="inline-block floatleft">
            <!-- No photo to show -->
            <div class="floatleft inline-block valignmiddle divphotoref">
                <div class="photoref">
                    <!-- <span class="fas fa-project-diagram  em088 infobox-project" style="" title="No photo"></span> -->
                    '.img_picto('', 'generic').'
                </div>
            </div>
        </div>
        <div class="inline-block floatleft valignmiddle maxwidth750 marginbottomonly refid refidpadding">
            <div class="refidno">
                ';
                print $langs->trans('ThirdParty').' : '.$societe->getNomUrl(1, '');

                print '<br/>'.$langs->trans('Phone').' : '.dol_print_phone($societe->phone, $societe->country_code, $contactid, $societe->id, 'AC_TEL', '&nbsp;', 'phone', $langs->trans("Phone"));

                print '<br/>'.$langs->trans('Email').' : '.dol_print_email($societe->email, $societe->id, $societe->id, 'AC_EMAIL', 0, 0, 1);

                print '<br/>'.$langs->trans('Project').' : '.$projectstatic->getNomUrl(1);
                print '
            </div>
        </div>
    </div>
    <!-- End banner content -->
</div>';
print '<div class="underbanner clearboth"></div>';

$site->getOtherSite();

// print '<pre>'.print_r($site, true).'</pre>';

if($action == 'update'){



}else{
    print '<script type="text/javascript">
    function collapseelement(idtable, idelement){
        if($("."+idtable).attr("hidden") == "hidden"){
            $("."+idtable).attr("hidden", false);
            $("#"+idelement+"").addClass("fa-minus-square").removeClass("fa-plus-square");
        }else{
            $("."+idtable).attr("hidden", true);
            $("#"+idelement+"").addClass("fa-plus-square").removeClass("fa-minus-square");
        }
    }
    function addtr(type, idtable, idmodule){
        var c = $("#"+idtable+" tr").length;
        c = c + 1;
        if(type == "devspe"){
            $("#"+idtable+" > tbody:last-child").append("<tr id=\"devspe_"+idmodule+"_"+c+"\"><td style=\"color: green; display: flex; align-items: center\">'.$langs->trans('DevSpe').': <textarea name=\"devspe["+idmodule+"][]\" rows=\"1\" cols=\"50\"></textarea></td><td style=\"width: 10%;\"><span onclick=\"removetr(\'devspe_"+idmodule+"_"+c+"\')\" class=\"fas fa-trash\" title=\"'.$langs->trans("Delete").'\"></span></td>");
        }

        if(type == "param"){
            $("#"+idtable+" > tbody:last-child").append("<tr id=\"param_"+idmodule+"_"+c+"\"><td style=\"color: rgb(5 53 117); display: flex; align-items: center\">'.$langs->trans('Parametrage').': <textarea name=\"param["+idmodule+"][]\" rows=\"1\" cols=\"50\"></textarea></td><td style=\"width: 10%;\"><span onclick=\"removetr(\'param_"+idmodule+"_"+c+"\')\" class=\"fas fa-trash\" title=\"'.$langs->trans("Delete").'\"></span></td>");
        }

        if(type == "extra"){
            $("#"+idtable+" > tbody:last-child").append("<tr id=\"extra_"+idmodule+"_"+c+"\"><td style=\"display: flex; align-items: center\">Extrafields: <textarea name=\"extrafields["+idmodule+"][]\" rows=\"1\" cols=\"50\" placeholder=\"libellé // type // taille // valeur // visibilité // ligne ou objet\"></textarea></td><td style=\"width: 10%;\"><span onclick=\"removetr(\'extra_"+idmodule+"_"+c+"\')\" class=\"fas fa-trash\" title=\"'.$langs->trans("Delete").'\"></span></td>");
        }
    }

    function removetr(idtr){
        $("#"+idtr).remove();
    }

    function checkboxclick(e){
		if (!e) var e = window.event;                // Get the window event
		e.cancelBubble = true;                       // IE Stop propagationif (e.stopPropagation)
		e.stopPropagation();  // Other Broswers
	}
    </script>';

    $sql = "SELECT ci.fk_module, c.rowid, c.libelle, c.description";
    $sql .= " FROM ".MAIN_DB_PREFIX."avimm_constante c";
    $sql .= " INNER JOIN ".MAIN_DB_PREFIX."avimm_constante_inmodule ci ON ci.fk_constante = c.rowid";
    $resql = $db->query($sql);

    $arrayconstante = array();
    if($resql){
        foreach($resql as $row){
            $arrayconstante[$row['fk_module']][$row['rowid']]['libelle'] = $row['libelle'];
            $arrayconstante[$row['fk_module']][$row['rowid']]['description'] = $row['description'];
        }
    }
    // print '<pre>'.print_r($arrayconstante, true).'</pre>';

    print '<form method="post" action="'.$_SERVER['PHP_SELF'].'?id='.$id.'">';
    print '<input type="hidden" name="action" value="update">';
    print '<input type="hidden" name="token" value="'.newToken().'">';

    if($site->fk_logiciel != 4){
        // ligne avec btn pour cacher/afficher la liste des modules d'un site et Nom + type du site
        print '<div onclick="collapseelement(\'site_'.$site->rowid.'\', \'sitebtn_'.$site->rowid.'\')"><span id="sitebtn_'.$site->rowid.'" class="cursorpointer far fa-plus-square"></span>&nbsp;<div style="display: inline; font-weight: bold; font-size: 1.5em">Site '.$site->nom.' : '.$site->type.'</div></div>';

        print '<table class="site_'.$site->rowid.'" style="width: 60%;" hidden="hidden">';
        print '<br/>';

        $sql = "SELECT rowid, libelle FROM ".MAIN_DB_PREFIX."avimm_constante_module WHERE fk_logiciel = ".$site->fk_logiciel;
        $resql = $db->query($sql);

        $parametragesite = new ParametrageSite($db);
        $parametragesite->fetch($site->rowid);
        // print '<pre>'.print_r($parametragesite, true).'</pre>';
        // exit;

        foreach($resql as $module){
            print '<tr>';
            print '<td>';
            print '<table class="centpercent">';
            print '<tr onclick="collapseelement(\'module_'.$module['rowid'].'\',  \'modulebtn_'.$module['rowid'].'\')">';
            print '<td>';
            // ligne avec checkbox, btn pour cacher/afficher le contenu d'un module + Nom du module
            print '<input type="hidden" name="module['.$module['rowid'].']" value="off">'; // si case en dessous pas cocher, enverra la valeur off pour ce module
            print '<input name="module['.$module['rowid'].']" type="checkbox" onclick="checkboxclick(event)" '.($parametragesite->modules[$module['rowid']]['checked'] == 1 ? 'checked' : '').'>
            <div style="display: inline;">
                <!--<span id="modulebtn_'.$module['rowid'].'" class="cursorpointer far fa-minus-square"></span>&nbsp;-->
                <div style="display: inline; font-weight: bold;">'.$module['libelle'].'</div>
            </div>';
            print '</td>';
            print '</tr>';

            print '<tr class="module_'.$module['rowid'].'">';
            print '<td>';
            foreach($arrayconstante[$module['rowid']] as $constanteid => $constante){
                // ligne avec checkbox, btn pour cacher/afficher le contenu d'une constante + Nom de la constante
                print '<table class="centpercent">';
                print '<tr title="'.htmlspecialchars($constante['description']).'">';
                print '<td style="padding-left: 20px"><input name="constante['.$constanteid.']" type="checkbox" '.($parametragesite->modules[$module['rowid']][$constanteid]['checked'] == 1 ? 'checked' : '').'>';
                print '<input type="hidden" name="constante['.$constanteid.']" value="off">'; // si le check box au dessus pas cocher, enverra la valeur off pour cette constante
                print '<div style="display: inline; font-weight: bold; color: rgb(190 35 35)">'.$constante['libelle'].'</div>';
                print '</td>';
                print '</tr>';
                print '</table>';

            }
            print '</td>';
            print '</tr>';


            // ligne extrafields
            print '<tr class="module_'.$module['rowid'].'">';
            print '<td style="padding-left: 20px;">';

            print '<table id="tableextrafields_'.$module['rowid'].'" class="centpercent">';
            if($parametragesite->modules[$module['rowid']]['extrafields']){
                $c = 1;
                foreach($parametragesite->modules[$module['rowid']]['extrafields'] as $extrafield){
                    print '<tr id="extrafields_'.$module['rowid'].'_'.$c.'">';
                    print '<td style="display: flex; align-items: center">Extrafields: ';
                    print '<textarea name="extrafields['.$module['rowid'].'][]" rows="1" cols="50">'.$extrafield->description.'</textarea></td>';
                    print '<td style="width: 10%;">';
                    if($c == 1){
                        print '<span onclick="addtr(\'extra\', \'tableextrafields_'.$module['rowid'].'\', '.$module['rowid'].')" class="fa fa-plus-circle valignmiddle paddingleft" title="'.$langs->trans("Add").'"></span>';
                    }else{
                        print '<span onclick="removetr(\'extrafields_'.$module['rowid'].'_'.$c.'\')" class="fas fa-trash" title="'.$langs->trans("Delete").'"></span>';
                    }
                    print '</td>';
                    print '</tr>';

                    $c++;
                }
            }else{
                print '<tr id="extrafields_'.$module['rowid'].'_1">';
                print '<td style="display: flex; align-items: center">Extrafields: ';
                print '<textarea name="extrafields['.$module['rowid'].'][]" rows="1" cols="50" placeholder="libellé // type // taille // valeur // visibilité // ligne ou objet"></textarea></td>';
                print '<td style="width: 10%;">';
                print '<span onclick="addtr(\'extra\', \'tableextrafields_'.$module['rowid'].'\', '.$module['rowid'].')" class="fa fa-plus-circle valignmiddle paddingleft" title="'.$langs->trans("Add").'"></span>';
                print '</td>';
                print '</tr>';
            }
            print '</table>';

            print '</td>';
            print '</tr>'; // fin ligne extrafields

            // ligne dev spe
            print '<tr class="module_'.$module['rowid'].'">';
            print '<td style="padding-left: 20px;">';

            print '<table id="tabledevspe_'.$module['rowid'].'" class="centpercent">';
            if($parametragesite->modules[$module['rowid']]['devspe']){
                $c = 1;
                foreach($parametragesite->modules[$module['rowid']]['devspe'] as $devspe){
                    print '<tr id="devspe_'.$module['rowid'].'_'.$c.'">';
                    print '<td style="color: green; display: flex; align-items: center">'.$langs->trans('DevSpe').': ';
                    print '<textarea name="devspe['.$module['rowid'].'][]" rows="1" cols="50">'.$devspe->description.'</textarea></td>';
                    print '<td style="width: 10%;">';
                    if($c == 1){
                        print '<span onclick="addtr(\'devspe\', \'tabledevspe_'.$module['rowid'].'\', '.$module['rowid'].')" class="fa fa-plus-circle valignmiddle paddingleft" title="'.$langs->trans("Add").'"></span>';
                    }else{
                        print '<span onclick="removetr(\'devspe_'.$module['rowid'].'_'.$c.'\')" class="fas fa-trash" title="'.$langs->trans("Delete").'"></span>';
                    }
                    print '</td>';
                    print '</tr>';

                    $c++;
                }
            }else{
                print '<tr id="devspe_'.$module['rowid'].'_1">';
                print '<td style="color: green; display: flex; align-items: center">'.$langs->trans('DevSpe').': ';
                print '<textarea name="devspe['.$module['rowid'].'][]" rows="1" cols="50"></textarea></td>';
                print '<td style="width: 10%;">';
                print '<span onclick="addtr(\'devspe\', \'tabledevspe_'.$module['rowid'].'\', '.$module['rowid'].')" class="fa fa-plus-circle valignmiddle paddingleft" title="'.$langs->trans("Add").'"></span>';
                print '</td>';
                print '</tr>';
            }
            print '</table>';

            print '</td>';
            print '</tr>'; // fin ligne dev spe

            // ligne parametre
            print '<tr class="module_'.$module['rowid'].'">';
            print '<td style="padding-left: 20px;">';

            print '<table id="tableparam_'.$module['rowid'].'" class="centpercent">';
            if($parametragesite->modules[$module['rowid']]['param']){
                $c = 1;
                foreach($parametragesite->modules[$module['rowid']]['param'] as $param){
                    print '<tr id="param_'.$module['rowid'].'_'.$c.'">';
                    print '<td style="color: rgb(5 53 117); display: flex; align-items: center">'.$langs->trans('Parametrage').': ';
                    print '<textarea name="param['.$module['rowid'].'][]" rows="1" cols="50">'.$param->description.'</textarea></td>';
                    print '<td style="width: 10%;">';
                    if($c == 1){
                        print '<span onclick="addtr(\'param\', \'tableparam_'.$module['rowid'].'\', '.$module['rowid'].')" class="fa fa-plus-circle valignmiddle paddingleft" title="'.$langs->trans("Add").'"></span>';
                    }else{
                        print '<span onclick="removetr(\'param_'.$module['rowid'].'_'.$c.'\')" class="fas fa-trash" title="'.$langs->trans("Delete").'"></span>';
                    }
                    print '</td>';
                    print '</tr>';

                    $c++;
                }
            }else{
                print '<tr id="param_'.$module['rowid'].'_1">';
                print '<td style="color: rgb(5 53 117); display: flex; align-items: center;">'.$langs->trans('Parametrage').': ';
                print '<textarea name="param['.$module['rowid'].'][]" rows="1" cols="50"></textarea></td>';
                print '<td style="width: 10%;">';
                print '<span onclick="addtr(\'param\', \'tableparam_'.$module['rowid'].'\', '.$module['rowid'].')" class="fa fa-plus-circle valignmiddle paddingleft" title="'.$langs->trans("Add").'"></span>';
                print '</td>';
                print '</tr>';
            }
            print '</table>';

            print '</td>';
            print '</tr>'; // fin ligne parametre

            print '</table>'; // fin table du module

            print '</td>';
            print '</tr>';
        }
    }else{
        print '<div style="font-weight: bold; font-size: 1.5em">Site '.$site->nom.' : '.$site->type.'</div>';
        print 'Rien pour logiciel Autre';
    }

    print '</table>';

    print $form->buttonsSaveCancel("Save", "");

    print '</form>';

    // Boutons d'action
    print '<div class="tabsAction">'."\n";

    // Bouton pour créer une démo
    print dolGetButtonAction('', $langs->trans('CreateDemo'), 'default', $_SERVER["PHP_SELF"].'?id='.$site->rowid.'&action=createdemo&token='.newToken(), '', 1);

    print '</div>';

}
