<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/custom/advancedictionaries/includes/parsedown/" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/custom/agefodd/includes/Psr/simple-cache/src" isTestSource="false" packagePrefix="Psr\SimpleCache\" />
      <sourceFolder url="file://$MODULE_DIR$/custom/ecommerceng/includes" isTestSource="false" packagePrefix="Automattic" />
      <sourceFolder url="file://$MODULE_DIR$/custom/ecommerceng/patchs/dolibarr/includes" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/src" isTestSource="false" packagePrefix="Google\" />
      <sourceFolder url="file://$MODULE_DIR$/custom/multicompany/lib/PHP_Markdown/Michelf" isTestSource="false" packagePrefix="Michelf\" />
      <sourceFolder url="file://$MODULE_DIR$/custom/sirene/includes/parsedown/" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/htdocs" isTestSource="false" packagePrefix="Dolibarr\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/Psr/simple-cache/src" isTestSource="false" packagePrefix="Psr\SimpleCache\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/maximebf/debugbar/src/DebugBar" isTestSource="false" packagePrefix="DebugBar\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/mike42/escpos-php/src/Mike42" isTestSource="false" packagePrefix="Mike42\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/mobiledetect/mobiledetectlib/namespaced" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/includes/parsedown/" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/includes/restler/framework/Luracast/Restler/" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/includes/sabre/psr/log/Psr/Log" isTestSource="false" packagePrefix="Psr\Log\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/sabre/sabre/dav/lib/CalDAV" isTestSource="false" packagePrefix="Sabre\CalDAV\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/sabre/sabre/dav/lib/CardDAV" isTestSource="false" packagePrefix="Sabre\CardDAV\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/sabre/sabre/dav/lib/DAV" isTestSource="false" packagePrefix="Sabre\DAV\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/sabre/sabre/dav/lib/DAVACL" isTestSource="false" packagePrefix="Sabre\DAVACL\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/sabre/sabre/event/lib" isTestSource="false" packagePrefix="Sabre\Event\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/sabre/sabre/http/lib" isTestSource="false" packagePrefix="Sabre\HTTP\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/sabre/sabre/uri/lib" isTestSource="false" packagePrefix="Sabre\Uri\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/sabre/sabre/vobject/lib" isTestSource="false" packagePrefix="Sabre\VObject\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/sabre/sabre/xml/lib" isTestSource="false" packagePrefix="Sabre\Xml\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/sabre/sabre/xml/tests/Sabre/Xml" isTestSource="true" packagePrefix="Sabre\Xml\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/stripe/stripe-php/lib" isTestSource="false" packagePrefix="Stripe\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/stripe/stripe-php/tests" isTestSource="true" packagePrefix="Stripe\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/stripe/stripe-php/tests/Stripe" isTestSource="true" packagePrefix="Stripe\" />
      <sourceFolder url="file://$MODULE_DIR$/includes/swiftmailer/lexer/lib" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/includes/symfony/var-dumper/" isTestSource="false" packagePrefix="Symfony\Component\VarDumper\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/guzzlehttp/guzzle/src" isTestSource="false" packagePrefix="GuzzleHttp\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/guzzlehttp/guzzle/tests" isTestSource="true" packagePrefix="GuzzleHttp\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/guzzlehttp/promises/src" isTestSource="false" packagePrefix="GuzzleHttp\Promise\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/guzzlehttp/promises/tests" isTestSource="true" packagePrefix="GuzzleHttp\Promise\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/guzzlehttp/psr7/src" isTestSource="false" packagePrefix="GuzzleHttp\Psr7\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/guzzlehttp/psr7/tests" isTestSource="true" packagePrefix="GuzzleHttp\Tests\Psr7\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/ovh/ovh/src" isTestSource="false" packagePrefix="Ovh\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/ovh/ovh/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/psr/http-message/src" isTestSource="false" packagePrefix="Psr\Http\Message\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/ralouphie/getallheaders/tests" isTestSource="true" packagePrefix="getallheaders\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/symfony/polyfill-intl-idn/" isTestSource="false" packagePrefix="Symfony\Polyfill\Intl\Idn\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/symfony/polyfill-intl-normalizer/" isTestSource="false" packagePrefix="Symfony\Polyfill\Intl\Normalizer\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/symfony/polyfill-php70/" isTestSource="false" packagePrefix="Symfony\Polyfill\Php70\" />
      <sourceFolder url="file://$MODULE_DIR$/ovh/includes/symfony/polyfill-php72/" isTestSource="false" packagePrefix="Symfony\Polyfill\Php72\" />
      <sourceFolder url="file://$MODULE_DIR$/test" isTestSource="true" packagePrefix="Dolibarr\Tests\" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/automattic/woocommerce" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/dompdf/dompdf" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/dompdf/php-font-lib" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/dompdf/php-svg-lib" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/ezyang/htmlpurifier" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/hubspot/api-client" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/maennchen/zipstream-php" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/markbaker/complex" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/markbaker/matrix" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/masterminds/html5" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/mpdf/mpdf" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/mpdf/psr-http-message-shim" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/mpdf/psr-log-aware-trait" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/myclabs/php-enum" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/phpoffice/math" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/phpoffice/phpspreadsheet" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/phpoffice/phpword" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/sabberworm/php-css-parser" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/setasign/fpdi" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/avimm/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/firebase/php-jwt" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/google/apiclient-services" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/google/auth" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/paragonie/constant_time_encoding" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/phpseclib/phpseclib" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/symfony/polyfill-php70" />
      <excludeFolder url="file://$MODULE_DIR$/custom/google/includes/google-api-php-client/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/custom/sirene/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/custom/sirene/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/custom/sirene/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/custom/sirene/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/custom/sirene/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/custom/sirene/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/custom/sirene/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/custom/sirene/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/custom/sirene/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/custom/sirene/vendor/symfony/polyfill-php70" />
      <excludeFolder url="file://$MODULE_DIR$/custom/sirene/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/ovh/includes/composer" />
      <excludeFolder url="file://$MODULE_DIR$/ovh/includes/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/ovh/includes/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/ovh/includes/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/ovh/includes/ovh/ovh" />
      <excludeFolder url="file://$MODULE_DIR$/ovh/includes/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/ovh/includes/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/ovh/includes/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/ovh/includes/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/ovh/includes/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/ovh/includes/symfony/polyfill-php70" />
      <excludeFolder url="file://$MODULE_DIR$/ovh/includes/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/instantiator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-invoker" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/cli-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/code-unit" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/complexity" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/lines-of-code" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/resource-operations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/theseer/tokenizer" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>