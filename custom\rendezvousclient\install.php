<?php
/**
 *	\file       rendezvousclient/install.php
 *	\ingroup    rendezvousclient
 *	\brief      Script d'installation du module rendezvousclient
 */

require_once '../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

$action = GETPOST('action', 'az09');

/*
 * Actions
*/

if($action == 'install'){
    $error = 0;
    
    // Créer la table pour la synthèse CDC
    $sql = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."rendez_vous_synthese_cdc (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_projet integer NOT NULL,
        fk_source_cdc integer,
        version varchar(10),
        date_creation datetime,
        date_demo datetime,
        date_livraison datetime,
        nb_rdv_amont varchar(100),
        nb_rdv_aval varchar(100),
        nb_jours_formations varchar(100),
        contexte_projet text,
        objectifs_principaux text,
        perimetre_projet text,
        fonctionnalites_principales text,
        contraintes_techniques text,
        ressources_mobilisees text,
        roles_responsabilites text,
        delais_globaux text,
        jalons_importants text,
        criteres_acceptation text,
        procedure_validation text,
        budget_estimatif text,
        modalites_paiement text
    ) ENGINE=innodb";
    
    $resql = $db->query($sql);
    if(!$resql){
        $error++;
        setEventMessages($langs->trans("ErrorCreatingTable")." llx_rendez_vous_synthese_cdc: ".$db->lasterror(), null, 'errors');
    }
    
    // Créer la table pour les démos
    $sql = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."rendez_vous_demo (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_projet integer NOT NULL,
        demo_name varchar(255) NOT NULL,
        demo_url varchar(500),
        demo_path varchar(500),
        demo_db_name varchar(255),
        modules_selectionnes text,
        date_creation datetime,
        date_expiration datetime,
        statut tinyint DEFAULT 1,
        notes text
    ) ENGINE=innodb";
    
    $resql = $db->query($sql);
    if(!$resql){
        $error++;
        setEventMessages($langs->trans("ErrorCreatingTable")." llx_rendez_vous_demo: ".$db->lasterror(), null, 'errors');
    }
    
    if($error == 0){
        setEventMessages($langs->trans("InstallationCompleted"), null, 'mesgs');
    }
}

/*
 * View
*/

llxHeader('', $langs->trans("InstallModule"));

print load_fiche_titre($langs->trans("InstallModule"), '', 'setup');

print '<div class="info">';
print $langs->trans("InstallModuleDescription");
print '</div>';

print '<form method="post" action="'.$_SERVER['PHP_SELF'].'">';
print '<input type="hidden" name="action" value="install">';
print '<input type="hidden" name="token" value="'.newToken().'">';

print '<div class="center">';
print '<input type="submit" class="button" value="'.$langs->trans("Install").'">';
print '</div>';

print '</form>';

// Vérifier l'état des tables
print '<br><h3>'.$langs->trans("TablesStatus").'</h3>';

$tables_to_check = array(
    'rendez_vous_synthese_cdc',
    'rendez_vous_demo'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>'.$langs->trans("Table").'</th>';
print '<th>'.$langs->trans("Status").'</th>';
print '</tr>';

foreach($tables_to_check as $table){
    $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
    $resql = $db->query($sql);
    
    print '<tr>';
    print '<td>'.MAIN_DB_PREFIX.$table.'</td>';
    
    if($resql && $db->num_rows($resql) > 0){
        print '<td><span class="badge badge-status4 badge-status">'.$langs->trans("Exists").'</span></td>';
    } else {
        print '<td><span class="badge badge-status8 badge-status">'.$langs->trans("NotExists").'</span></td>';
    }
    
    print '</tr>';
}

print '</table>';

// End of page
llxFooter();
$db->close();
