# Copyright (C) 2025 SuperAdmin
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.

#
# Generic
#

# Module label 'ModuleRendezvousclientName'
ModuleRendezvousclientName = Rendez-vous client
rendezvousclient = Rendez-vous client
# Module description 'ModuleRendezvousclientDesc'
ModuleRendezvousclientDesc = Rendez-vous client description

#
# Admin page
#
RendezvousclientSetup = Rendez-vous client setup
Settings = Settings
RendezvousclientSetupPage = Rendez-vous client setup page
RENDEZVOUSCLIENT_MYPARAM1 = My param 1
RENDEZVOUSCLIENT_MYPARAM1Tooltip = My param 1 tooltip
RENDEZVOUSCLIENT_MYPARAM2=My param 2
RENDEZVOUSCLIENT_MYPARAM2Tooltip=My param 2 tooltip


#
# About page
#
About = About
RendezvousclientAbout = A propos module Rendez-vous client
RendezvousclientAboutPage = Rendez-vous client page a propos

#
# Sample page
#
RendezvousclientArea = Accueil module Rendez-vous client
MyPageName = My page name

#
# Sample widget
#
MyWidget = My widget
MyWidgetDescription = My widget description

#
# RENDEZ-VOUS
#
RDVlong = Rendez-vous client
FormMultiSite = Formulaire de multisites
Multisites = Multisites

StatutPlanifie = Planifié
StatutRealise = Réalisé
StatutAnnule = Annulé
StatutReporte = Reporté

NewRDVClient = Nouveau RDV client
NewRDVClientlong = Nouveau rendez-vous client
ListRDVClient = Liste RDV client
FormSuiviClient = Formulaire suivi RDV client

NbRDV = Nombre de rendez-vous client
NumeroRDV = Numéro du rendez-vous
NumeroRDVshort = N° rdv
DateRDV = Date rendez-vous
DateD = Date démo
ObjRDV = Objet du rendez-vous
NotePrivateProjet = Note privée du projet
CompteRendu = Compte rendu du rendez-vous

ChezClient = Chez le client
Appel = Appel
Visio = En visioconférence

Hebergement = Hébergement
ObjectifsClient = Objectifs du client
BesoinsClient = Besoins du client
ReponseBesoinClient = Réponses aux besoins du client

createformulaire = Créer formulaire
createcahiercharge = Créer cahier des charges
CahierCharge = Cahier des charges
SyntheseCDC = Synthèse CDC
createdevis = Créer devis


#
# SITE
#

Site = Site
NewSite = Nouveau site
ListSite = Liste des sites

NbSite = Nombre de site
NomSite = Nom du site
TypeSite = Type de site
NbUser = Nombre d'utilisateurs
TypeDescriptifUser = Types et descriptifs utilisateurs
Type = Type
Descriptif = Descriptif
LogicielPourSite = Logiciel pour site
DevSpe = Développement spécifique
Parametrage = Paramètrage

#
# Cahier des charges
#

Cdc = Cahier des charges
ListCdc = Liste cahier des charges
GenerateCdc = Générer cahier des charges
source_cdc = Cahier des charges source
version_num = Numéro de version

nb_rdv_amont = Nombre de rdv en amont
nb_rdv_aval = Nombre de rdv en aval
nb_jours_formations = Nombre de jours de formations

Introduction = Introduction
intro_contexte_projet = Contexte du projet
intro_objectifs_globaux = Objectifs globaux
intro_presentation_client = Présentation du client

Perimetre_projet = Périmètre du projet
perimetre_projet_delimitation = Délimitation des sites ou services concernés
perimetre_projet_def_processus = Définitions des processus métiers à digitaliser

Besoins_fonctionnels = Besoins fonctionnels
besoins_processus_par_site = Processus métier par site
besoins_fonctionnels_user = Fonctionnalités spécifiques pour chaque type d’utilisateur

Architecture_tech_logicielle = Architecture technique et logicielle
architecture_solutions = Présentation des solutions retenus (Dolibarr et VTiger)
architecture_modules = Architecture des modules et integrations
architecture_infrastructure = Infrastructure et hébergement

Gestion_info_process = Gestion des échanges d’informations et automatisation des processus
process_av_vente = Workflow de l’avant-vente, de la vente et des points de vente
flux_logistiques = Flux logistiques : stocks et approvisionnements
prod_et_qualite = Processus de production et contrôle qualité
commandes_et_expeditions = Traitement des commandes e-commerce et expéditions
creation_recettes = Cycle de création des recettes (R&D)
reporting_analyse = Reporting et analyse

Interface_user_experience = Interface utilisateur et experience
ux_roles = Expérience utilisateur pour chaque type de role
ux_interfaces = Interface et navigation adaptées aux différents sites et équipements
ux_kpi_client = KPI client

Formation_docu = Formation et documentation
formation_programme = Programme de formation par type de role
formation_documentation = Documentation technique et guide utilisateur

Maintenace_support = Maintenance et support
maintenance_support = Plan de support et d’assisitance technique
maintenance_mises_a_jour = Mise à jour et évolutions logicielles

Deploiement_planning = Deploiement et planning
deploiement_calendrier = Calendrier prévisionnel des différentes phases du projet
deploiement_jalons = Etapes de validation et jalons clés

Critere_reussite = Critères de réussite
succes_kpi = Indicateurs de performance (KPI) pour mesurer l’efficacité des solutions
succes_suivi = Suivi post-déploiement et ajustements

budget_previsionnel = Budget prévisionnel

modifs_recommandations = Modification et recommandations

Mentions_legal_validation = Mentions légales et validation
mentions_confidentialite = Confidentialité
mentions_propriete = Propriété intellectulle
mentions_conditions_modification = Conditions de modification

#
# Synthèse CDC
#
SyntheseCDCUpdated = Synthèse CDC mise à jour
ErrorSyntheseCDCNotUpdated = Erreur lors de la mise à jour de la Synthèse CDC
GeneralInformation = Informations générales
DateCDC = Date du cahier des charges
OrganisationEchanges = Organisation des échanges et accompagnement
PresentationObjectifs = Présentation et objectifs du projet
ContexteProjet = Contexte du projet
ObjectifsPrincipaux = Objectifs principaux du projet
PerimetreProjet = Périmètre du projet
DescriptionBesoins = Description des besoins
FonctionnalitesPrincipales = Fonctionnalités principales attendues
ContraintesTechniques = Contraintes et aspects techniques
RessourcesOrganisation = Ressources et organisation
RessourcesMobilisees = Ressources mobilisées (équipe, outils, etc.)
RolesResponsabilites = Rôles et responsabilités (client, prestataire)
DelaisJalons = Délais et jalons clés
DelaisGlobaux = Délais globaux
JalonsImportants = Jalons importants (démarrage, démo, livraison, recette, etc.)
CriteresValidation = Critères de validation et modalités de recette
CriteresAcceptation = Critères d'acceptation des livrables
ProcedureValidation = Procédure de validation
BudgetModalites = Budget et modalités de paiement
BudgetEstimatif = Budget estimatif
ModalitesPaiement = Modalités de paiement (acompte, échéances, etc.)
MentionLegale = Ce document, qui constitue une proposition technique et commerciale, n'a pas valeur contractuelle. Le cahier des charges détaillé sera transmis une fois le devis validé.
CreateDemo = Créer démo
DemoCreationStarted = La création de la démo a été lancée
DemoCreatedSuccessfully = Démo créée avec succès
ErrorDemoCreation = Erreur lors de la création de la démo
DemosList = Liste des démos
DemoName = Nom de la démo
DemoURL = URL de la démo
DateExpiration = Date d'expiration
OpenDemo = Ouvrir la démo
ConfirmDeleteDemo = Êtes-vous sûr de vouloir supprimer cette démo ?
DemoDeleted = Démo supprimée
ErrorDemoNotDeleted = Erreur lors de la suppression de la démo
NoDemo = Aucune démo trouvée
Demos = Démos

#
# Installation
#
InstallModule = Installation du module
InstallModuleDescription = Ce script va créer les tables nécessaires pour le bon fonctionnement du module rendezvousclient.
InstallationCompleted = Installation terminée avec succès
ErrorCreatingTable = Erreur lors de la création de la table
TablesStatus = État des tables
Table = Table
Exists = Existe
NotExists = N'existe pas
