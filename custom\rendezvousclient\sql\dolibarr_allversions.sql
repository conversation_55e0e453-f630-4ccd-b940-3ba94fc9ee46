--
-- <PERSON>rip<PERSON> run when an upgrade of <PERSON><PERSON><PERSON><PERSON> is done. Whatever is the Dolibarr version.
--

-- Création de la table pour la synthèse CDC
CREATE TABLE IF NOT EXISTS llx_rendez_vous_synthese_cdc (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_projet integer NOT NULL,
  fk_source_cdc integer,
  version varchar(10),
  date_creation datetime,
  date_demo datetime,
  date_livraison datetime,
  nb_rdv_amont varchar(100),
  nb_rdv_aval varchar(100),
  nb_jours_formations varchar(100),
  contexte_projet text,
  objectifs_principaux text,
  perimetre_projet text,
  fonctionnalites_principales text,
  contraintes_techniques text,
  ressources_mobilisees text,
  roles_responsabilites text,
  delais_globaux text,
  jalons_importants text,
  criteres_acceptation text,
  procedure_validation text,
  budget_estimatif text,
  modalites_paiement text
) ENGINE=innodb;

-- Création de la table pour les démos créées
CREATE TABLE IF NOT EXISTS llx_rendez_vous_demo (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_projet integer NOT NULL,
  demo_name varchar(255) NOT NULL,
  demo_url varchar(500),
  demo_path varchar(500),
  demo_db_name varchar(255),
  modules_selectionnes text,
  date_creation datetime,
  date_expiration datetime,
  statut tinyint DEFAULT 1,
  notes text
) ENGINE=innodb;
