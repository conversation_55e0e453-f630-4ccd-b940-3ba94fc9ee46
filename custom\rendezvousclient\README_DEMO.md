# Système de Création de Démos Dolibarr

## Vue d'ensemble

Le module rendezvousclient inclut un système automatisé de création de démos Dolibarr. Ce système permet de générer des instances de démonstration personnalisées basées sur les modules et fonctionnalités sélectionnés dans le paramétrage des sites.

## Fonctionnalités

### 1. Création automatique de démos
- Génération d'instances Dolibarr isolées
- Configuration automatique des modules sélectionnés
- Support pour Docker et méthodes alternatives

### 2. Gestion des démos
- Liste de toutes les démos créées
- Suivi des dates de création et d'expiration
- Suppression des démos obsolètes

### 3. Intégration avec les sites
- Bouton "Créer démo" dans la page de paramétrage des sites
- Prise en compte de tous les sites d'un client pour les démos multisites
- Lien automatique entre projets et démos

## Installation

### Prérequis
1. Dolibarr installé et fonctionnel
2. Module rendezvousclient activé
3. Docker installé (optionnel mais recommandé)

### Étapes d'installation
1. Exécuter le script d'installation : `/custom/rendezvousclient/install.php`
2. Vérifier que les tables ont été créées correctement
3. Configurer les permissions d'accès aux répertoires de démo

## Configuration

### Configuration Docker (Recommandée)
Si Docker est disponible sur votre système, le module utilisera automatiquement cette méthode pour créer des instances isolées.

**Avantages :**
- Isolation complète des démos
- Gestion automatique des ports
- Facilité de suppression

**Configuration requise :**
- Docker et Docker Compose installés
- Ports 8080+ disponibles
- Accès en écriture au répertoire de données

### Configuration Alternative
Si Docker n'est pas disponible, le module utilisera une méthode alternative qui crée des pages de redirection vers des démos en ligne.

## Utilisation

### Créer une démo
1. Aller dans un site existant
2. Cliquer sur l'onglet "Paramétrage"
3. Sélectionner les modules souhaités
4. Cliquer sur "Créer démo"
5. Attendre la confirmation de création

### Gérer les démos
1. Aller dans le menu "Démos" du module rendezvousclient
2. Consulter la liste des démos créées
3. Ouvrir les démos en cliquant sur l'URL
4. Supprimer les démos obsolètes

### Intégration avec les devis
Les démos créées peuvent être intégrées dans les propositions commerciales via la Synthèse CDC.

## Structure des données

### Table llx_rendez_vous_demo
- `rowid` : ID unique de la démo
- `fk_projet` : Lien vers le projet
- `demo_name` : Nom unique de la démo
- `demo_url` : URL d'accès à la démo
- `demo_path` : Chemin local de la démo
- `demo_db_name` : Nom de la base de données de démo
- `modules_selectionnes` : JSON des modules sélectionnés
- `date_creation` : Date de création
- `date_expiration` : Date d'expiration
- `statut` : Statut de la démo (1=active, 0=inactive)
- `notes` : Notes additionnelles

## Sécurité

### Considérations de sécurité
- Les démos sont créées avec des identifiants par défaut (admin/admin)
- Les démos ont une durée de vie limitée (30 jours par défaut)
- Les démos sont isolées du système principal
- Accès restreint aux utilisateurs autorisés

### Recommandations
- Nettoyer régulièrement les démos expirées
- Surveiller l'utilisation des ressources
- Limiter le nombre de démos simultanées
- Utiliser HTTPS pour les démos en production

## Dépannage

### Problèmes courants

**Erreur : "Docker non disponible"**
- Vérifier que Docker est installé et démarré
- Vérifier les permissions d'exécution
- Utiliser la méthode alternative si nécessaire

**Erreur : "Impossible de créer le répertoire"**
- Vérifier les permissions d'écriture
- Vérifier l'espace disque disponible
- Vérifier la configuration des chemins

**Démo inaccessible**
- Vérifier que le conteneur Docker est démarré
- Vérifier la configuration des ports
- Vérifier les logs du conteneur

### Logs et débogage
- Logs Docker : `docker logs [nom_conteneur]`
- Logs Dolibarr : Répertoire de logs standard
- Logs système : `/var/log/` ou équivalent Windows

## Maintenance

### Nettoyage automatique
Un script de nettoyage peut être configuré pour supprimer automatiquement les démos expirées.

### Sauvegarde
Les démos étant temporaires, elles ne nécessitent généralement pas de sauvegarde. Cependant, les informations de configuration sont stockées dans la base de données principale.

### Mise à jour
Lors des mises à jour du module, vérifier la compatibilité des démos existantes et les recréer si nécessaire.

## Support

Pour toute question ou problème concernant le système de création de démos, consulter :
1. Cette documentation
2. Les logs du système
3. La documentation Dolibarr officielle
4. Le support technique du module

## Développement

### Extension du système
Le système est conçu pour être extensible. Vous pouvez :
- Ajouter de nouveaux types de démos
- Personnaliser la configuration des modules
- Intégrer avec d'autres systèmes
- Modifier les modèles de démo

### API
Le système expose des méthodes pour :
- Créer des démos programmatiquement
- Gérer le cycle de vie des démos
- Intégrer avec d'autres modules

## Changelog

### Version 1.0
- Création du système de base
- Support Docker
- Interface de gestion des démos
- Intégration avec les sites et projets
