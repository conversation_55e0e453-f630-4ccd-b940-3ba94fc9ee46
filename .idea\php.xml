<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/doctrine/instantiator" />
      <path value="$PROJECT_DIR$/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/vendor/sebastian/resource-operations" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/avimm/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/avimm/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/avimm/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/avimm/vendor/composer" />
      <path value="$PROJECT_DIR$/avimm/vendor/setasign/fpdi" />
      <path value="$PROJECT_DIR$/avimm/vendor/phpoffice/math" />
      <path value="$PROJECT_DIR$/avimm/vendor/phpoffice/phpword" />
      <path value="$PROJECT_DIR$/avimm/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/avimm/vendor/sabberworm/php-css-parser" />
      <path value="$PROJECT_DIR$/avimm/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/avimm/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/avimm/vendor/dompdf/php-font-lib" />
      <path value="$PROJECT_DIR$/avimm/vendor/psr/log" />
      <path value="$PROJECT_DIR$/avimm/vendor/dompdf/php-svg-lib" />
      <path value="$PROJECT_DIR$/avimm/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/avimm/vendor/dompdf/dompdf" />
      <path value="$PROJECT_DIR$/avimm/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/avimm/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/avimm/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/avimm/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/avimm/vendor/mpdf/mpdf" />
      <path value="$PROJECT_DIR$/avimm/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/avimm/vendor/mpdf/psr-log-aware-trait" />
      <path value="$PROJECT_DIR$/avimm/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/avimm/vendor/mpdf/psr-http-message-shim" />
      <path value="$PROJECT_DIR$/avimm/vendor/hubspot/api-client" />
      <path value="$PROJECT_DIR$/avimm/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/avimm/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/avimm/vendor/myclabs/php-enum" />
      <path value="$PROJECT_DIR$/avimm/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/avimm/vendor/automattic/woocommerce" />
      <path value="$PROJECT_DIR$/avimm/vendor/masterminds/html5" />
      <path value="$PROJECT_DIR$/avimm/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/custom/sirene/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/custom/sirene/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/custom/sirene/vendor/symfony/polyfill-php70" />
      <path value="$PROJECT_DIR$/custom/sirene/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/custom/sirene/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/custom/sirene/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/custom/sirene/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/custom/sirene/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/custom/sirene/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/custom/sirene/vendor/composer" />
      <path value="$PROJECT_DIR$/custom/sirene/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/ovh/includes/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/ovh/includes/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/ovh/includes/ovh/ovh" />
      <path value="$PROJECT_DIR$/ovh/includes/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/ovh/includes/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/ovh/includes/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/ovh/includes/symfony/polyfill-php70" />
      <path value="$PROJECT_DIR$/ovh/includes/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/ovh/includes/composer" />
      <path value="$PROJECT_DIR$/ovh/includes/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/ovh/includes/psr/http-message" />
      <path value="$PROJECT_DIR$/ovh/includes/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/phpseclib/phpseclib" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/psr/log" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/symfony/polyfill-php70" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/firebase/php-jwt" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/composer" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/google/auth" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/google/apiclient-services" />
      <path value="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/monolog/monolog" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="7.0">
    <option name="suggestChangeDefaultLanguageLevel" value="false" />
  </component>
  <component name="PhpUnit">
    <phpunit_settings>
      <PhpUnitSettings custom_loader_path="$PROJECT_DIR$/custom/google/includes/google-api-php-client/vendor/autoload.php" />
      <PhpUnitSettings configuration_file_path="$PROJECT_DIR$/phpunit.xml.dist" custom_loader_path="$PROJECT_DIR$/vendor/autoload.php" use_configuration_file="true" />
    </phpunit_settings>
  </component>
</project>